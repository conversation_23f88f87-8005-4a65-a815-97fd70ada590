<template>
    <div class="medicinal-box">
        <div class="content-list">
            <div class="content" v-for="(item, index) in list.slice(0, 3)" :key="index">
                <div class="content-left">{{ item.label }}</div>
                <div class="content-right text-ellipsis">{{ item.value }}</div>
            </div>
        </div>
        <div class="opt-btn" @click="onOpen">
            查看更多
        </div>
    </div>
    <van-popup round :show="show" teleport="body" position="bottom" @close="onClose"
        :safe-area-inset-bottom='true' lock-scroll>
        <div class="popup-container">
            <div class="popup-header">说明书</div>
            <div class="close-icon" @click="onClose">
                <van-icon name="close" color="#999999" size="18px"/>
            </div>
            <div class="popup-content">
                <template  v-for="(item, index) in list" :key="item.key">
                    <div class="content" v-if="!isNullOrUnDef(item.value)">
                        <div class="content-left">{{ item.label }}</div>
                        <div class="content-right">
                            <div class="right-text" v-if="!item.isShowDropdown">
                                {{ item.value }}
                            </div>
                            <div class="right-text" v-else>
                                {{ !item.isAll ? item.value.slice(0, 50) : item.value }}
                            </div>
                            <template v-if="item.isShowDropdown">
                                <div class="opt-more" v-if="!item.isAll" @click="handleDropdown(item)">
                                    展开全文
                                </div>
                                <div class="opt-more_in" v-else @click="handleDropdown(item)">
                                    收起
                                </div>
                            </template>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </van-popup>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";
import { isObject, isString,isNullOrUnDef } from "@/utils/isUtils";
import { queryDosageForm } from "@/services/api/product";
import { storeToRefs } from "pinia";
import { useUserStore } from "@/stores/modules/user";
import { useMessages } from "@/hooks/useMessage";
const { token } = storeToRefs(useUserStore())
const message = useMessages()
const props = withDefaults(defineProps<{
    state: any
}>(), {
    state: () => ({})
})
const emits = defineEmits<{
    (e: 'update:show', val: boolean): void;
}>()
const show = ref<boolean>(false)
const topicalMap = {
    0: "否",
    1: "是",
}
const originMap = {
    1: "国产",
    2: "进口",
}
const dosageMap = ref<Record<number, string>>({})
const list = ref<any[]>([
    {
        key: "name",
        label: "药品通用名",
        value: "",
    },
    {
        key: "frontName",
        label: "商品名",
        value: "",
    },
    {
        key: "manufacturer",
        label: "生产厂家",
        value: "",
    },
    {
        key: "brandName",
        label: "品牌",
        value: "",
    },
    {
        key: "uses",
        label: "功能主治",
        value: "",
    },
    {
        key: "dosage",
        label: "用法用量",
        value: "",
    },
    {
        key: "adverseReaction",
        label: "不良反应",
        value: "",
    },
    {
        key: "contraindications",
        label: "禁忌",
        value: "",
    },
    {
        key: "precautions",
        label: "注意事项",
        value: "",
    },
    {
        key: "drugInteraction",
        label: "药物互相作用",
        value: "",
    },
    {
        key: "storage",
        label: "贮藏",
        value: "",
    },
    {
        key: "topical",
        label: "是否外用",
        value: "",
    },
    {
        key: "composition",
        label: "成份",
        value: "",
    },
    {
        key: "description",
        label: "性状",
        value: "",
    },
    {
        key: "unitDose",
        label: "剂型",
        value: "",
    },
    {
        key: "drugPackage",
        label: "包装",
        value: "",
    },
    {
        key: "validity",
        label: "有效期",
        value: "",
    },
    {
        key: "origin",
        label: "产地类型",
        value: "",
    },
    {
        key: "approvalNumber",
        label: "批准文号",
        value: "",
    },
    {
        key: "executiveStandard",
        label: "执行标准",
        value: "",
    },
    {
        key: "reminder",
        label: "温馨提示",
        value: "",
    },
])
const onClose = () => {
    show.value = false
}
const onOpen = () => {
    show.value = true
}
const handleDropdown = (item: any) => {
    item.isAll = !item.isAll
}
const getDosageData = async () => {
    try {
        const res = await queryDosageForm()
        dosageMap.value = res
    } catch (error) {
        message.createMessageError(`获取失败：${error}`)
    }
}
watch(() => [props.state, dosageMap], () => {
    if (isObject(props.state)) {
        list.value.forEach(item => {
            if (item.key === "origin") {
                item.value = originMap[props.state[item.key]]
            } else if (item.key === 'topical') {
                item.value = topicalMap[props.state[item.key]]
            } else if (item.key === 'unitDose') {
                item.value = dosageMap.value[props.state[item.key]]
            } else {
                item.value = props.state[item.key]
            }
            if (isString(item.value) && item.value.length > 50) {
                item.isShowDropdown = true
                item.isAll = false
            } else {
                item.isShowDropdown = false
                item.isAll = true
            }
        })
    }
}, {
    deep: true,
    immediate:true
})
watch(() => token.value, () => {
    getDosageData()
})
onMounted(() => {
    if (token.value) {
        getDosageData()
    }
})
</script>

<style scoped lang="less">
@import "@/styles/default.less";
.content {
    width: 100%;
    margin-bottom: 8px;
    display: flex;
    font-size: 14px;

    .content-left {
        min-width: 70px;
        text-align: left;
        color: #666;
        margin-right: 10px;
    }
}

.medicinal-box {
    color: #666;
    font-size: 12px;
    width: 100%;

    .content-list {}

    .opt-btn {
        padding: 10px 0;
        text-align: center;
    }
}

.popup-container {
    width: 100%;
    height: 100%;
    font-size: 14px;
    padding: 13px 12px 5px;
    box-sizing: border-box;
    position: relative;

    .popup-header {
        font-size: 16px;
        display: flex;
        width: 100%;
        justify-content: center;
        align-items: center;
        margin-bottom: 12px;
        padding-bottom: 10px;
    }
    .close-icon{
        width: min-content;
        position: absolute;
        top: 12px;
        right: 12px;
        cursor: pointer;
    }

    .popup-content {
        max-height: 75vh;
        overflow-y: scroll;

        .content-right {
            .right-text{
                color: #333333;
                word-break: break-all;
            }
            .opt-more_in,
            .opt-more {
                font-weight: bold
            }

            .opt-more {
                color: @primary-color;
            }

            .opt-more_in {}
        }
    }
}
</style>