import { ref, computed, watch, reactive } from "vue";
import type { Ref } from "vue";
import { storeToRefs } from "pinia";
import { useMessages } from "@/hooks/useMessage";
import { filterSkuMinWelfare } from "@/utils/storeUtils";
import { couponNonPres } from "@/services/storeApi/order";
import { StoreGoodsEnum } from "@/enums/storeGoods";
import { useRoute } from "vue-router";
interface Props {
  goodsInfo: Ref<any>;
  /**是否监听商品数据 */
  isWatchGoods?: boolean;
  productIdField?: string;
}

export type BtnType = "cart" | "shop";

export default function useWelfare({ goodsInfo, isWatchGoods = true, productIdField = "id" }: Props) {
  const route = useRoute();
  const message = useMessages();
  const cartNum = ref<number>(1);
  const curSku = ref<string>("");
  const isLoading = ref<boolean>(false);
  const skuList = computed<any[]>(() => {
    let list = goodsInfo.value?.couponProductSpecList || [];
    return list.map(item => {
      return {
        ...item,
        disabled: !item.availStock || item.isDeleted == 1,
      };
    });
  });
  const curGoodsInfo = computed(() => {
    const info = skuList.value.find(item => item.id == curSku.value) || {};
    return {
      ...info,
      availStocks: info?.availStock || 0,
      exchangePrice: info?.exchangePrice || 0,
      upper: info?.limitPerPurchase || 0,
    };
  });
  const handleSaveCheck = () => {
    if (!curSku.value) {
      message.createMessageInfo("请选择规格！");
      return false;
    }
    if (curGoodsInfo.value.isDeleted == 1) {
      message.createMessageInfo("规格已删除！");
      return false;
    }
    if (!curGoodsInfo.value.availStocks || cartNum.value > curGoodsInfo.value.availStocks) {
      message.createMessageInfo("库存不足!");
      return false;
    }
    if (!curGoodsInfo.value.upper || cartNum.value > curGoodsInfo.value.upper) {
      message.createMessageInfo(`购买上限为${curGoodsInfo.value.upper}，不能超过购买上限！`);
      return false;
    }
    return true;
  };
  /**订单确定 */
  const handleConfirmOrder = async (callback?: (info: { type: StoreGoodsEnum; orderInfo: string }) => void) => {
    if (!handleSaveCheck()) {
      return;
    }
    try {
      isLoading.value = true;
      const params: any = {
        data: {
          cartItemVOList: [
            {
              productId: goodsInfo.value[productIdField],
              exchangeCount: curGoodsInfo.value.exchangeCount,
              specId: curSku.value,
              count: cartNum.value,
              couponCateId: curGoodsInfo.value.couponCateId,
            },
          ],
        },
      };
      if (route.query.liveId) {
        params.data.liveId = route.query.liveId;
      }
      const notPresRes = await couponNonPres(params);
      const orderInfo = {
        cartItemDTOList: notPresRes.cartItemDTOList,
        totalCoupons: notPresRes.totalCoupons,
        requestNo: notPresRes.requestNo,
        storeEntity: notPresRes?.storeEntity || null, // 店铺信息
        customerAddressDTO: notPresRes?.customerAddressDTO || null, // 收货地址
      };
      const routeInfo = {
        orderInfo: encodeURIComponent(JSON.stringify(orderInfo)),
        type: StoreGoodsEnum.WelfareTicket,
      };
      callback && callback(routeInfo);
    } catch (error) {
      message.createMessageError(error);
    } finally {
      isLoading.value = false;
    }
  };

  const setMinSkuData = () => {
    const info = filterSkuMinWelfare(skuList.value);
    curSku.value = info.id;
  };

  //初始化sku数据
  watch(
    () => goodsInfo.value,
    () => {
      if (!isWatchGoods) {
        return;
      }
      curSku.value = "";
      setMinSkuData();
    },
    {
      deep: true,
    },
  );
  return {
    curSku,
    cartNum,
    curGoodsInfo,
    skuList,
    setMinSkuData,
    handleConfirmOrder,
    isLoading,
  };
}
