import { ref, computed, watch, reactive } from "vue";
import type { Ref } from "vue";
import { storeToRefs } from "pinia";
import { useMessages } from "@/hooks/useMessage";
import { filterSkuMinIntegral, transformSpecData } from "@/utils/storeUtils";
import { pointNonPres } from "@/services/storeApi/order";
import { StoreGoodsEnum } from "@/enums/storeGoods";
import { useRoute } from "vue-router";

interface Props {
  goodsInfo: Ref<any>;
  /**是否监听商品数据 */
  isWatchGoods?: boolean;
}

export type BtnType = "cart" | "shop";

export default function ({ goodsInfo, isWatchGoods = true }: Props) {
  const route = useRoute();
  const message = useMessages();
  const cartNum = ref<number>(1);
  const curSku = ref<string>("");
  /** 选中的规格属性id集 */
  const selectedSpecIds = ref({
    firstAttrId: "",
    secondAttrId: "",
    thirdAttrId: "",
  });
  /** 所选规格是否未在积分规格中 */
  const isNotInIntegral = ref<boolean>(false);
  const isLoading = ref<boolean>(false);
  const skuList = computed<any[]>(() => {
    let list = goodsInfo.value?.appletPointSpecDTOS || [];
    return list.map(item => {
      return {
        ...item,
        disabled: !item.availStocks || item.isDeleted == 1,
      };
    });
  });
  const curGoodsInfo = computed(() => {
    const info = skuList.value.find(item => item.id == curSku.value) || {};
    return {
      ...info,
      availStocks: info?.availStocks || 0,
      exchangePrice: info?.exchangePrice || 0,
      upper: info?.upper || 0,
    };
  });
   /** 商品属性列表 */
  const SpecAttrList = computed(() => {
    return transformSpecData(goodsInfo.value?.productAttributeValueDTOList || []);
  });
  const handleSaveCheck = () => {
    if (!curSku.value) {
      message.createMessageInfo("请选择规格！");
      return false;
    }
    if (curGoodsInfo.value.isDeleted == 1) {
      message.createMessageInfo("规格已删除！");
      return false;
    }
    if (!curGoodsInfo.value.availStocks || cartNum.value > curGoodsInfo.value.availStocks) {
      message.createMessageInfo("库存不足!");
      return false;
    }
    if (!curGoodsInfo.value.upper || cartNum.value > curGoodsInfo.value.upper) {
      message.createMessageInfo(`购买上限为${curGoodsInfo.value.upper}，不能超过购买上限！`);
      return false;
    }
    if (!curGoodsInfo.value.exchangePoints) {
      message.createMessageInfo("订单积分总额不能少于0!");
      return false;
    }
    return true;
  };
  /**订单确定 */
  const handleConfirmOrder = async (callback?: (info: {type:StoreGoodsEnum,orderInfo: string}) => void) => {
    if (!handleSaveCheck()) {
      return;
    }
    try {
      isLoading.value = true;
      const params = {
        data: curSku.value,
      };
      const notPresRes = await pointNonPres(params);
      const orderInfo = {
        cartItemDTOList:notPresRes.cartItemDTOList,
        goodsAmount:notPresRes.goodsAmount,
        money:notPresRes.money,
        returnPoints:notPresRes.returnPoints,
        shippingFee:notPresRes.shippingFee,
        totalPoints:notPresRes.totalPoints,
        requestNo:notPresRes.requestNo,
        storeEntity: notPresRes?.storeEntity || null, // 店铺信息
        customerAddressDTO: notPresRes?.customerAddressDTO || null, // 收货地址
      };
      const routeProps = {
        orderInfo: encodeURIComponent(JSON.stringify(orderInfo)),
        type:StoreGoodsEnum.IntegralGoods,
      };
      callback && callback(routeProps);
    } catch (error) {
      message.createMessageError(error);
    } finally {
      isLoading.value = false;
    }
  };

  const setMinSkuData = () => {
    const info = filterSkuMinIntegral(skuList.value);
    curSku.value = info.id;
  };
/** 修改选中id */
  const handleSelectedSpecIds = (val: any, key: string) => {
    selectedSpecIds.value[key] = val.id;
    isNotInIntegral.value = false;
    /** 如果没找到则isNotInIntegral改为true */
    const selectedSpecSku = skuList.value.find((item)=>{
      return item.firstAttrId == selectedSpecIds.value.firstAttrId &&
        item.secondAttrId == selectedSpecIds.value.secondAttrId &&
        item.thirdAttrId == selectedSpecIds.value.thirdAttrId
    })
    if(!selectedSpecSku){
      isNotInIntegral.value = true;
      return;
    }
    curSku.value = selectedSpecSku.id
  };
  //初始化sku数据
  watch(
    () => goodsInfo.value,
    () => {
      if (!isWatchGoods) {
        return;
      }
      curSku.value = "";
      setMinSkuData();
    },
    {
      deep: true,
    },
  );
  watch(
    () => curGoodsInfo.value,
    newV => {
      if (
        curGoodsInfo.value.firstAttrId != selectedSpecIds.value.firstAttrId ||
        curGoodsInfo.value.secondAttrId != selectedSpecIds.value.secondAttrId ||
        curGoodsInfo.value.thirdAttrId != selectedSpecIds.value.thirdAttrId
      ) {
        Object.assign(selectedSpecIds.value, {
          firstAttrId: newV.firstAttrId,
          secondAttrId: newV.secondAttrId,
          thirdAttrId: newV.thirdAttrId,
        });
      }
    },{
      deep: true,
      immediate: true,
    }
  );
  return {
    curSku,
    cartNum,
    curGoodsInfo,
    skuList,
    setMinSkuData,
    handleConfirmOrder,
    isLoading,
    SpecAttrList,
    selectedSpecIds,
    handleSelectedSpecIds,
    isNotInIntegral
  };
}
