import { getPoolLink } from "@/services/api/pool";
import { useUserStoreWithoutSetup } from "@/stores/modules/user";
import { useLocalTimeCalibration } from "@/views/Exam/hooks/useLocalTimeCalibration";

export async function createPoolLink(scene,link,expire = null) {
    const userStore = useUserStoreWithoutSetup()
    const urlPattern = /state=([^&]+)/;
    const match = link.match(urlPattern);
    if (match && match[1]) {
        const state = match[1];
        const params = {
            scene,
            dealerId:userStore.userInfo.dealerId,
            groupId:userStore.userInfo.id,
            state
        }
        if(expire){
            params.expire = expire
        }
        const resp = await getPoolLink(params)
        return resp

    } 
    else {
        throw new Error('State parameter not found in the URL');
    }
}
export function calcMaxExpireSecond(endTimestamp:number){
    const { getDateAfterCalibration } = useLocalTimeCalibration()
    const _nowDateTimestamp = getDateAfterCalibration().getTime()
    const diff = (endTimestamp - _nowDateTimestamp) 
    if(diff > 0){
        return Math.ceil(diff / 1000)
    }
    else{
        return 0
    }
}

export function judgeExpireSecond(endTimestamp:number,validTimeSec?:number){
    const maxVaild = calcMaxExpireSecond(endTimestamp)
    if(validTimeSec){
        if(validTimeSec>=maxVaild){
            return maxVaild
        }
        else{
            return validTimeSec
        }
    }
    else return maxVaild
   
}