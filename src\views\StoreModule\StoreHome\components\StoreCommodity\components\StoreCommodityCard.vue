<template>
  <div class="store_commodity_item">
    <VanImage :src="productInfoRef?.firstImg" fit="cover" lazy-load class="store_commodity_img" />
    <div class="store_commodity_info">
      <p class="store_commodity_item_name van-multi-ellipsis--l2">{{ productInfoRef?.frontName }}</p>
      <div class="purchase">
        <div class="store_commodity_item_price">
          <StorePrice :price="getItemLowestPrice(productInfoRef)['price']" />
          <!-- 划线价格 -->
          <span v-if="getItemLowestPrice(productInfoRef)['marketPrice']" class="store_commodity_item_original_price">
            {{`￥${getItemLowestPrice(productInfoRef)['marketPrice']}`}}
          </span>
        </div>
        <VanButton round type="danger" size="small" style="width: 48px;height: 24px;border-radius: 25px;">
          抢购
        </VanButton>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { toRefs } from "vue";
import { getLowestPriceSku } from "../utils";
/** 相关组件 */
import StorePrice from "@/views/StoreModule/components/StorePrice.vue";

defineOptions({ name: 'StoreCommodityCard' });

/** props */
const props = defineProps<{
  productInfo: {
    id: number;
    frontName: string;
    appletProductSpecDTOList: {
      id: number;
      price: number;
      activityPriceVOList?: {
        activityPrice: number;
      };
    };
    firstImg: string;
  };
}>();

const { productInfo: productInfoRef } = toRefs(props);

/** 最小价格与划线价 */
const getItemLowestPrice = (item: any) => {
  if (!item?.appletProductSpecDTOList?.length) return 0;

  const lowestSku = getLowestPriceSku(item.appletProductSpecDTOList);
  if (lowestSku.marketPrice) {
    return {
      price: lowestSku.minPrice || lowestSku.price || 0,
      marketPrice: formatPrice(lowestSku.marketPrice),
    };
  } else {
    return {
      price: lowestSku.price || 0,
      marketPrice: null,
    }
  }
};

/** 将分转换为元并格式化显示 */
const formatPrice = (priceInCents: number) => {
  return (priceInCents / 100).toFixed(2);
};
</script>

<style lang="less" scoped>
.store_commodity_item {
  width: 100%;
  background: #FFFFFF;
  border-radius: 8px;
  margin-bottom: 8px;
  break-inside: avoid;

  .store_commodity_img {
    width: 100%;
    height: auto;
    aspect-ratio: 1/1;
    border-radius: 8px 8px 0 0;
    object-fit: cover;
  }

  .store_commodity_info {
    width: 100%;
    padding: 8px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .store_commodity_item_name {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #333333;
      line-height: 24px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-bottom: 8px;
    }
    .purchase {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .store_commodity_item_price {
        display: flex;
        flex-wrap: wrap;
        align-items: flex-end;
        font-family: BebasNeue;
        gap: 4px;
        .store_commodity_item_original_price {
          font-family: DIN Pro, DIN Pro;
          font-weight: 400;
          font-size: 12px;
          color: #999999;
          line-height: 20px;
          text-align: left;
          font-style: normal;
          text-decoration-line: line-through;
          text-transform: none;
        }
      }
    }
  }
}
</style>
