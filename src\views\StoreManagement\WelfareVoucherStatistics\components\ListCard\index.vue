<template>
    <div class="listWarrper">
        <div :class="['card',{'cardBg':props.showDetail}]">
            <div class="cardPadding">
                <div class="info">
                    <van-image round width="18" height="18" fit="contain" :src="Certificate" v-if="props.showDetail" style="margin-right: 8px;"/>
                    <div class="name">
                        <div :class="['quan',{'quanDetail':!props.showDetail}]">
                            <div class="title">{{ props.showDetail ? props.data.couponName : props.data.csNickname}}</div>
                            <div class="idStyle">
                            ID:
                            <div class="idText">{{  props.showDetail ? props.data.couponId : props.data.csShortId || '-'}} </div>
                            <div class="copyBtn" @click="(e)=>handleCopy(e,props.showDetail ? props.data.couponId : props.data.csShortId)">
                                <van-image round width="15" height="15" fit="contain" :src="CopyBtn"/>
                            </div>
                        </div>
                        </div>
                        <div style="display:flex;font-size: 14px;color: #666666;" v-if="props.showDetail" @click="(e)=>handleJumpDetail(e,props.data)">
                            会员详情
                            <van-icon name="arrow" />
                        </div>
                    </div>
                </div>
                <div class="content">
                    <div class="item" v-for="item in list">
                        <div style="color: #333333;font-size:20px">{{item.num}}</div>
                        <div style="margin-top:4px;color: #666666;">{{item.label}}</div>
                    </div>
                </div>
            </div>
        </div>
       
    </div>
</template>
<script setup lang="ts">
import { ref,reactive,watch } from "vue";
// import {  type GetDataByTypeResponse } from "@/services/api/welfareVoucherStatistics";
import type { GetDataByTypeResponse } from "@/views/StoreManagement/WelfareVoucherStatistics/hooks/index";
import Certificate from "@/assets/store0602Image/certificate.png"
import CopyBtn from "@/assets/store0602Image/copyBtn.png"
import { copyText } from "@/utils/clipboardUtils";
import { useMessages } from "@/hooks/useMessage";
import { useRouter,useRoute } from "vue-router"
import { RoutesName } from "@/enums/routes";
import { routesMap } from "@/router/maps";
const router = useRouter()
const route = useRoute()
const {createMessageSuccess,createMessageError,createMessageWarning,createMessageInfo} = useMessages()
type ListCardProps = {
    data:GetDataByTypeResponse,
    showDetail:boolean,
    searchParams:any
}
const props = withDefaults(defineProps<ListCardProps>(),{
    value:()=>(
        {
            couponName:'',
            couponId:'',
            getNum:0,
            unUseNum:0,
            useNum:0,
            invalidNum:0,
            csNickname:'',
            csId:''

        } as GetDataByTypeResponse
    ),
    showDetail:false,
    searchParams:{
        staffId:'',
        csId:'',
    }
})
const list = reactive([
    {num:props.data.getNum,label:'领取数量'},
    {num:props.data.unUseNum,label:'待使用'},
    {num:props.data.useNum,label:'已使用'},
    {num:props.data.invalidNum,label:'已失效'},
])
// 复制
function handleCopy(e,con){
    e.stopPropagation()
    try{
        copyText(con)
        createMessageSuccess('复制ID成功')
    }
    catch(e){
        createMessageError('复制ID失败')
    }
}
function handleJumpDetail (e,data){
    e.stopPropagation()
    const _routeInfo = routesMap[RoutesName.StoreWelfareVoucherStatisticsDetail]
    const params = {
        ...route.query,
        couponName:data.couponName || '-',
        couponId:data.couponId || '',
        staffId:props.searchParams.staffId || '',
        csId:props.searchParams.csId || '',
    }
    _routeInfo.query = {..._routeInfo.query,...params}
    router.push(_routeInfo)
}
</script>
<style scoped lang="less">
.listWarrper{
    width:100%;
    font-size:15px;
    margin-bottom: 10px;
    .card{
        width:95%;
        background-color:white;
        margin:auto;
        border-radius: 15px;
        // background: linear-gradient(to bottom,#FFE3DF 0%, white 50%, white 100%);
        
        .cardPadding{
            padding:10px
        }
       .info{
            display: flex;
            justify-content: space-between;
            padding-bottom: 10px;
            border-bottom: 1px solid #EEEEEE;
            .name{
                width: 99%;
                display: flex;
                align-items: center;
                justify-content: space-between;
                .quan{
                    width: 75%;
                    .title{
                        font-weight: 700;
                        margin-right: 5px;
                        color: black;
                        font-size: 16px;
                    }
                }
                .quanDetail{
                    width: 100%;
                    display: flex;
                    align-items: center;
                }
                .idStyle{
                    font-size: 13px;
                    margin-top:5px;
                    display:flex;
                    color: #999999;
                    .idText{
                        margin-left: 5px;
                    }
                    .copyBtn{
                        color: #B51E2C;
                        font-weight: 600;
                        margin-left: 5px;
                    }
                }
            }
        }
        .content{
            margin-top:14px;
            display:flex;
            justify-content:space-around;
            font-size: 14px;
            .item{
                text-align:center;
            }
        }
    }
    .cardBg{
        background: linear-gradient(to bottom,#FFE3DF 0%, white 50%, white 100%);
    }
}

</style>