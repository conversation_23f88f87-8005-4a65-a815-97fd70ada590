import { routesMap } from "@/router/maps/index";
import { baseRoutesConfig } from "@/router/config/base.config";
import { getRoutesMapByConfig,syncBaseConfig } from "@/utils/routerUtils";
import { createCacheStorage } from "@/utils/cache/storageCache";
import { CacheConfig } from "@/utils/cache/config";
import { useUserStoreWithoutSetup } from "@/stores/modules/user";
import { isStoreMode } from "@/utils/envUtils";
const userStore = useUserStoreWithoutSetup()
const routeConfigStorage = createCacheStorage(CacheConfig.RouteConfig);
const _routeConfigCache = routeConfigStorage.get(isStoreMode()?'st':'sg');
if(!_routeConfigCache){
    userStore.clearLoginStatus()
}
syncBaseConfig(_routeConfigCache)
const _config = _routeConfigCache ? _routeConfigCache : baseRoutesConfig;
routeConfigStorage.set(_config);
export const routes = getRoutesMapByConfig(_config, routesMap);
