<template>
  <JLoadingWrapper :show="isPendingStatus || isLoading">
    <div class="cash-warpper">
      <!-- 金额展示区域 -->
      <div class="cash-header">
        <div class="cash-price">
          <span>￥</span>
          <span>{{ totalAmount }}</span>
        </div>
        <div class="cash-desc">需付款</div>
      </div>

      <!-- 支付方式选择区域 -->
      <div class="cash-footer">
        <div class="footer-header">支付方式</div>
        <div class="pay-list">
          <!-- 支付方式列表循环 -->
          <div v-for="item in cashList" :key="item.key" class="pay-info" @click="payTypeChange(item.key)">
            <!-- 支付方式图标 -->
            <img :src="item.icon" alt="" class="info-icon" />
            <div class="info-content">
              <div class="title">{{ item.name }}</div>
              <!-- 单选按钮 -->
              <VanRadio use-icon-slot :value="payType">
                <template #icon="props">
                  <img class="radio-icon" :src="props.checked ? radioActive : radioNormal" />
                </template>
              </VanRadio>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部确定按钮 -->
      <view class="footer-box">
        <VanButton round @click="handlePay" class="btn" :disabled="isPendingStatus || isLoading">确定</VanButton>
      </view>
    </div>
  </JLoadingWrapper>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { PayTypeEnum, StoreGoodsEnum } from "@/enums/storeGoods";
import { RoutesName } from "@/enums/routes";
import { usePayment } from "./hooks/usePayment";
import { useMessages } from "@/hooks/useMessage";
import { toCheckOut, pointToCheckOut } from "@/services/storeApi/order";
// 相关图片资源
import onlinePay from "@/assets/storeImage/product/confirmOrder/cashPay.png";
import radioActive from "@/assets/storeImage/product/confirmOrder/radioActive.png";
import radioNormal from "@/assets/storeImage/product/confirmOrder/radioNormal.png";
// 相关组件
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import { isInFrame } from "@/utils/envUtils";
import { useSystem } from "@/hooks/useSystem";

const router = useRouter();
const route = useRoute();
const message = useMessages();
const {dpDomainRef} = useSystem()
/** props */
const props = defineProps({
  // 商品类型
  type: {
    type: Number,
    default: StoreGoodsEnum.Goods, // 默认商品类型为普通商品
  },
  // 订单编号
  orderCode: {
    type: String,
    default: '', 
  }
});

/** 支付相关Hook */
const {
  WXPayFn,
  isPendingStatus,
  requestNoRef,
  orderCodeRef,
  pageNotifyUrlRef
} = usePayment(props.type);

/** 订单商品列表 */
const orderItemList = ref<any[]>([]);
/** 当前支付方式，默认在线支付 */
const payType = ref<PayTypeEnum>(PayTypeEnum.OnlinePay);
/** 是否处于加载中状态 */
const isLoading = ref(false);
/** 总金额，默认显示0.00 */
const totalAmount = ref('0.00');

/** 支付方式列表 */
const cashList = [
  {
    name: '在线支付',
    icon: onlinePay,
    key: PayTypeEnum.OnlinePay
  }
];

/**
 * 切换支付方式
 * @param type 支付方式枚举值
 */
const payTypeChange = (type: PayTypeEnum) => {
  payType.value = type;
}

/**
 * 获取订单结算信息
 */
const getToCheckOut = async () => {
  try {
    isLoading.value = true // 开始加载

    // 根据商品类型选择API
    const api = props.type == StoreGoodsEnum.IntegralGoods ? pointToCheckOut : toCheckOut;
    // 调用API获取订单信息
    const res = await api({ orderCode: props.orderCode });

    // 如果订单已支付，跳转到订单详情页
    if (res.errMsg?.includes("已支付")) {
      router.replace({
        name: RoutesName.StoreOrderDetail,
        query: {
          ...route.query,
          orderCode: props.orderCode
        }
      });
      return message.createMessageWarning(res.errMsg);
    }

    // 根据商品类型获取对应的支付信息
    const paymentDTO = props.type == StoreGoodsEnum.IntegralGoods
      ? res.mixPointPaymentDTO
      : res.onlinePaymentDTO;

    // 更新订单商品列表
    orderItemList.value = paymentDTO?.orderItemList || [];
    // 计算并格式化金额（分转元）
    const money = paymentDTO?.money || 0;
    totalAmount.value = (money / 100).toFixed(2);
    // 保存请求编号
    requestNoRef.value = res.requestNo;
  } catch (error) {
    // 错误处理
    message.createMessageError(`获取订单信息失败:${error}`);
  } finally {
    isLoading.value = false; // 结束加载
  }
}

/**
 * 处理支付操作
 */
const handlePay = async () => {
  // 获取商品ID用于构建返回URL
  const productId = orderItemList.value[0]?.productId;
  let backUrl = `${location.origin}/st/goodsDetail?type=${props.type}&id=${productId}`;

  // 如果是直播场景，使用直播返回URL
  if (route.query.isLive == '1') {
    backUrl = `${location.origin}/st/live?state=${route.query.state}`;
  }
  if(isInFrame() && dpDomainRef.value){
    backUrl = dpDomainRef.value
  }

  // 设置支付相关参数
  orderCodeRef.value = props.orderCode;
  pageNotifyUrlRef.value = backUrl;
  // 调用微信支付方法
  WXPayFn()
}

/** 组件挂载时获取订单信息 */
onMounted(() => {
  getToCheckOut();
});
</script>

<style scoped lang="less">
@import url('@/styles/storeVar.less');

.cash-warpper {
  padding: 12px;
  box-sizing: border-box;
  overflow: auto;
  background-color: #fff;
  height: calc(100vh - env(safe-area-inset-bottom));

  .cash-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 24px 0;

    .cash-price {
      font-size: 32px;
      font-weight: 600;
      color: #333333;
      padding: 12px 0;
    }

    .cash-desc {
      color: #666666;
      font-size: 14px;
    }
  }

  .cash-footer {
    .footer-header {
      font-size: 16px;
      font-weight: 600;
      color: #333333;
      margin-bottom: 22px;
    }

    .pay-list {
      .pay-info {
        display: flex;
        align-items: center;
        margin-bottom: 24px;

        .info-icon {
          width: 20px;
          height: 20px;
          margin-right: 12px;
        }

        .info-content {
          display: flex;
          align-items: center;
          padding: 16px 0;
          justify-content: space-between;
          flex: 1;
          border-bottom: 1px solid #EEEEEE;

          .title {
            font-size: 14px;
            font-weight: 400;
            color: #333333;
          }
        }
      }
    }
  }

  .radio-icon {
    width: 20px;
    height: 20px;
  }

  .footer-box {
    position: fixed;
    bottom: calc(24px + env(safe-area-inset-bottom));
    left: 0;
    width: 100vw;
    padding: 10px 10px 15px;
    box-sizing: border-box;

    .btn {
      width: 100%;
      background-color: @error-color;
      color: #fff;
    }
  }
}
</style>
