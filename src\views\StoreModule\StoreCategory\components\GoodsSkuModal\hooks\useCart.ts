import { ref, computed, watch, reactive } from "vue";
import type { Ref } from "vue";
import { storeToRefs } from "pinia";
import { isNullOrUnDef } from "@/utils/isUtils";
import { useUserStore } from "@/stores/modules/user";
import { useMessages } from "@/hooks/useMessage";
import { StoreGoodsEnum } from "@/enums/storeGoods";
import { contrastMinPriceSku, transformSpecData } from "@/utils/storeUtils";
import { nonPres } from "@/services/storeApi/order";
import { useRoute } from "vue-router";
interface Props {
  goodsInfo: Ref<any>;
  isWatchGoods?: boolean;
  productIdField?: string;
}

export type BtnType = "cart" | "shop";

export default function ({ goodsInfo, isWatchGoods = true, productIdField = "id" }: Props) {
  const route = useRoute();
  const message = useMessages();
  const cartNum = ref<number>(1);
  const curSku = ref<string>("");
  const isLoading = ref<boolean>(false);
  /** 选中的规格属性id集 */
  const selectedSpecIds = ref({
    firstAttrId: "",
    secondAttrId: "",
    thirdAttrId: "",
  });
  const skuList = computed<any[]>(() => {
    let list = goodsInfo.value?.appletProductSpecDTOList || [];
    list = JSON.parse(JSON.stringify(list));
    //添加规格最小售价
    list.forEach(item => {
      item.minPrice = item.price;
      if (!isNullOrUnDef(item.activityPrice) && item.activityPrice < item.price) {
        item.minPrice = item.activityPrice;
      }
    });
    return list.map(item => {
      return {
        ...item,
        disabled: !item.availStocks || item.isDeleted == 1 || goodsInfo.value.isPublish == 0,
      };
    });
  });
  const curGoodsInfo = computed(() => {
    let info = skuList.value.find(item => item.id == curSku.value) || {};
    return {
      ...info,
      availStocks: info?.availStocks || 0,
      price: info?.price || 0,
      minPrice: info?.minPrice || 0,
      exchangePrice: info?.exchangePrice || 0,
      upper: info?.upper || 0,
    };
  });
  /** 商品属性列表 */
  const SpecAttrList = computed(() => {
    return transformSpecData(goodsInfo.value?.appletProductAttributeValueDTOList || []);
  });
  //可省价格
  const discountPrice = computed(() => {
    const price = curGoodsInfo.value.price || 0;
    const activePrice = curGoodsInfo.value.activityPrice || 0;
    const result = price - activePrice;
    return (result / 100).toFixed(2);
  });
  //是否存在活动价格
  const isExistActivePrice = computed(() => {
    return !isNullOrUnDef(curGoodsInfo.value.activityPrice);
  });
  //立即购买添加购物车
  const handleCartPreOrderAdd = async (callback?: (params: { type: StoreGoodsEnum; orderInfo: string }) => void) => {
    try {
      isLoading.value = true;
      const params: any = {
        data: {
          cartItemVOList: [
            {
              productId: goodsInfo.value[productIdField],
              count: cartNum.value,
              specId: curSku.value,
              type: goodsInfo.value.type,
              version: curGoodsInfo.value.version,
              isVirtual: goodsInfo.value.isVirtual || 0,
            },
          ],
        },
      };
      if (route.query.liveId) {
        params.data.liveId = route.query.liveId;
      }
      const notPresRes = await nonPres(params);
      // const orderInfo = {
      //   cartItemDTOList:notPresRes.cartItemDTOList,
      //   goodsAmount:notPresRes.goodsAmount,
      //   money:notPresRes.money,
      //   returnPoints:notPresRes.returnPoints,
      //   shippingFee:notPresRes.shippingFee,
      //   requestNo:notPresRes['request-no'],
      //   storeEntity: notPresRes?.storeEntity || null, // 店铺信息
      //   customerAddressDTO: notPresRes?.customerAddressDTO || null, // 收货地址
      // }
      // Object.assign(notPresRes, {
      //   requestNo: notPresRes["request-no"],
      // });
      const routeInfo = {
        orderInfo: encodeURIComponent(JSON.stringify(notPresRes)),
        type: StoreGoodsEnum.Goods,
      };
      callback && callback(routeInfo);
    } catch (error) {
      message.createMessageError(`${error}`);
    } finally {
      isLoading.value = false;
    }
  };
  const handleSave = (callback?: (info: any) => void) => {
    if (goodsInfo.value.isPublish == 0) {
      message.createMessageWarning("商品已下架！");
      return;
    }
    if (!curSku.value) {
      message.createMessageWarning("请选择规格！");
      return;
    }
    if (curGoodsInfo.value.isDeleted == 1) {
      message.createMessageWarning("规格已删除！");
      return;
    }
    if (curGoodsInfo.value.price <= 0) {
      message.createMessageWarning(`商品价格低于0，无法下单！`);
      return;
    }
    if (!curGoodsInfo.value.availStocks || cartNum.value > curGoodsInfo.value.availStocks) {
      message.createMessageWarning(`库存不足`);
      return;
    }
    if (!curGoodsInfo.value.upper || cartNum.value > curGoodsInfo.value.upper) {
      message.createMessageWarning(`购买上限为${curGoodsInfo.value.upper}，不能超过购买上限！`);
      return;
    }
    handleCartPreOrderAdd(callback);
  };

  const setMinSkuData = () => {
    let info: any = contrastMinPriceSku(skuList.value);
    curSku.value = info.id;
  };
  const setSkuInfo = (skuId: string) => {
    curSku.value = skuId;
  };
  /** 修改选中id */
  const handleSelectedSpecIds = (val: any, key: string) => {
    selectedSpecIds.value[key] = val.id;
    curSku.value = skuList.value.find((item)=>{
      return item.firstAttrId == selectedSpecIds.value.firstAttrId &&
        item.secondAttrId == selectedSpecIds.value.secondAttrId &&
        item.thirdAttrId == selectedSpecIds.value.thirdAttrId
    }).id
  };

  //初始化sku数据
  watch(
    () => goodsInfo.value,
    () => {
      //初始化sku参数
      if (!isWatchGoods) {
        return;
      }
      curSku.value = "";
      setMinSkuData();
    },
    {
      deep: true,
    },
  );

  watch(
    () => curGoodsInfo.value,
    newV => {
      if (
        curGoodsInfo.value.firstAttrId != selectedSpecIds.value.firstAttrId ||
        curGoodsInfo.value.secondAttrId != selectedSpecIds.value.secondAttrId ||
        curGoodsInfo.value.thirdAttrId != selectedSpecIds.value.thirdAttrId
      ) {
        Object.assign(selectedSpecIds.value, {
          firstAttrId: newV.firstAttrId,
          secondAttrId: newV.secondAttrId,
          thirdAttrId: newV.thirdAttrId,
        });
      }
    },{
      deep: true,
      immediate: true,
    }
  );
  return {
    handleSave,
    curSku,
    cartNum,
    curGoodsInfo,
    skuList,
    discountPrice,
    isExistActivePrice,
    setMinSkuData,
    setSkuInfo,
    isLoading,
    SpecAttrList,
    selectedSpecIds,
    handleSelectedSpecIds
  };
}
