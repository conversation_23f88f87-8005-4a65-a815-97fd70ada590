<template>
    <JLoadingWrapper :show="isLoading">
        <div class="coupon-box">
            <template v-if="couponList.length || disabledCouponList.length">
                <div class="coupon-header" v-if="couponTipStatus">
                    <div class="header-line"></div>
                    <!-- <div class="header-tip" @click="getBestGroup" v-if="couponTipStatus === CouponTipEnum.Optimal"
                        :style="{ 'color': '#1677FF' }">
                        点击我，帮你选择最佳抵扣组合
                    </div> -->
                    <div class="header-tip" v-if="couponTipStatus === CouponTipEnum.Selected"
                        :style="{ 'color': '#999999' }">
                        已经选择最佳抵扣组合啦
                    </div>
                    <div class="header-tip" v-if="couponTipStatus === CouponTipEnum.None" :style="{ 'color': '#999999' }">
                        <span>暂无可使用的券，只能使用小于</span>
                        <span style="color: #EF1115;">{{ Number(_orderInfo.maxCashCouponAmt / 100).toFixed(2) }}</span>
                        <span>元的现金抵扣券</span>
                    </div>
                </div>
                <div class="coupon-content">
                    <div class="info" :class="{ 'shelve': item.disabled }" @click="handleClick(item)"
                        v-for="item in couponList" :key="item.id">
                        <div class="info-left">
                            <PriceContent :price="item.cashCouponAmt" :style="{color:item.disabled?'#666666':'#EF1115'}" :isSplitUnit="false" :price-font-size="26"
                                :price-pre-font-size="14" />
                        </div>
                        <div class="info-center">
                            <div class="header">
                                {{ item.couponName }}
                            </div>
                            <div class="time">有效期至{{ item.expiredTime }}</div>
                            <div class="desc">本次现金抵扣额度以实际抵扣为准。</div>
                        </div>
                        <div class="info-right">
                            <van-checkbox :model-value="item.checked" checked-color="#EF1115"></van-checkbox>
                        </div>
                    </div>
                    <div class="info" :class="{ 'shelve': item.disabled }" @click="handleClick(item)"
                        v-for="item in disabledCouponList" :key="item.id">
                        <div class="info-left">
                            <PriceContent :price="item.cashCouponAmt" :style="{color:item.disabled?'#666666':'#EF1115'}" :isSplitUnit="false" :price-font-size="26"
                                :price-pre-font-size="14" />
                        </div>
                        <div class="info-center">
                            <div class="header">
                                {{ item.couponName }}
                            </div>
                            <div class="time">有效期至{{ item.expiredTime }}</div>
                            <div class="desc">本次现金抵扣额度以实际抵扣为准。</div>
                        </div>
                        <div class="info-right">
                            <van-checkbox :model-value="item.checked" checked-color="#EF1115"></van-checkbox>
                        </div>
                    </div>
                </div>
            </template>
            <van-empty :image-size="[260, 260]" :image="EmptyImage" v-else description="暂无可用现金券，快去获得吧！" />
            <div class="coupon-footer">
                <van-button v-if="couponTipStatus === CouponTipEnum.None || !couponList.length" type="danger" style="width: 100%;" round @click="handleBack">返回</van-button>
                <div class="footer-content" v-else>
                    <div class="footer-left">
                        <div class="text">可抵扣：</div>
                        <PriceContent :price="totalPrice" :price-pre-font-size="12" :isSplitUnit="false"
                            :price-font-size="24" />
                    </div>
                    <div class="footer-right" @click="handleSave">
                        确认
                    </div>
                </div>
            </div>
        </div>
    </JLoadingWrapper>
</template>

<script setup lang="ts">
import { ref, onMounted, computed,watch, reactive } from "vue";
import { useMessages } from "@/hooks/useMessage";
import PriceContent from "../components/PriceContent.vue";
import { queryMyCashCoupon, queryMyBestCashCoupon } from "@/services/storeApi/order";
import {  useRouter } from "vue-router";
import JLoadingWrapper from "@/components/JLoadingWrapper/index.vue";
import { RoutesName } from "@/enums/routes";
import EmptyImage from "@/assets/storeImage/product/coupon/empty.png";
const enum CouponTipEnum {
    /**最优选择 */
    Optimal,
    /**已选择最优 */
    Selected,
    /**没选择 */
    None
}
const router = useRouter();
const props = withDefaults(defineProps<{
    orderInfo: any
}>(), {
    orderInfo: () => ({})
})
const message = useMessages();
const isLoading = ref<boolean>(false)
const _orderInfo = ref({
    cartItemDTOList: [],
    selectedCashCouponIdList: [],
    selectIds:[],
    money: 0,
    goodsAmount: 0,
    totalPoints: 0,
    shippingFee: 0,
    returnPoints: 0,
    requestNo: null,
    totalCoupons: 0,
    maxCashCouponAmt: 0,
    cashCouponAmt: 0,
    storeEntity: {},
})
const bestIds = ref<string[]>([])
const couponTipStatus = ref(null);
const couponList = ref<any[]>([])
//不可用的列表
const disabledCouponList = ref<any[]>([])
const totalPrice = computed<number>(() => {
    return couponList.value.filter(item => item.checked && !item.disabled).reduce((total, item) => {
        return total + item.cashCouponAmt
    }, 0)
})
const checkIds = computed<string[]>(() => {
    return couponList.value.filter(item => item.checked && !item.disabled).map(item => item.id)
})
const getList = async () => {
    try {
        isLoading.value = true
        let res = await queryMyCashCoupon()
        if (!res.couponReceiveRecordList) {
            return
        }
        const couponReceiveRecordList = res?.couponReceiveRecordList.map(item => {
            return {
                ...item,
                checked:bestIds.value.includes(item.id),
                disabled: _orderInfo.value.maxCashCouponAmt <= 0 || item.cashCouponAmt > _orderInfo.value.maxCashCouponAmt
            }
        })
        couponList.value = couponReceiveRecordList.filter(item => !item.disabled).sort((a, b) => b.cashCouponAmt - a.cashCouponAmt)
        disabledCouponList.value = couponReceiveRecordList.filter(item => item.disabled).sort((a, b) => b.cashCouponAmt - a.cashCouponAmt)
        if(_orderInfo.value.maxCashCouponAmt > 0) {
            // getBestGroup()
            changeStatus()
            disabledCoupon()
        }else{
            //禁用所有
            couponList.value.forEach(item => {
                item.checked = false
                item.disabled = true
            })
            changeStatus()
        }
    } catch (error) {
        message.createMessageError(`获取失败：${error}`)
    } finally {
        isLoading.value = false
    }
}
const getBestGroup = async () => {
    try {
        isLoading.value = true
        const params = {
            maxCashCouponAmt: _orderInfo.value.maxCashCouponAmt,
        }
        const res: string[] = await queryMyBestCashCoupon(params)
        bestIds.value = res
        couponList.value.forEach(item => {
            item.checked = bestIds.value.includes(item.id)
            item.disabled =  _orderInfo.value.maxCashCouponAmt <= 0 || item.cashCouponAmt > _orderInfo.value.maxCashCouponAmt
        })
        changeStatus()
        disabledCoupon()
    } catch (error) {
        message.createMessageError(error)
    } finally {
        isLoading.value = false
    }
}
function disabledCoupon() {
    couponList.value.forEach(item => {
        if (checkIds.value.includes(item.id)) {
            return
        }
        item.disabled = (item.cashCouponAmt + totalPrice.value) > _orderInfo.value.maxCashCouponAmt
    })
}
function changeStatus() {
    const isAllDisabled = couponList.value.every(item => item.disabled)
    const isBest = bestIds.value.length && checkIds.value.length === bestIds.value.length && checkIds.value.every(item => bestIds.value.includes(item))
    if(_orderInfo.value.maxCashCouponAmt <= 0 || isAllDisabled){
        couponTipStatus.value = CouponTipEnum.None
        return
    }
    if(isBest){
        couponTipStatus.value = CouponTipEnum.Selected
        return
    }
    // couponTipStatus.value = CouponTipEnum.Optimal
    couponTipStatus.value = null
}
const handleClick = (info) => {
    info.checked = !info.checked
    changeStatus()
    disabledCoupon()
}
const handleSave = () => {
    // if (_orderInfo.value.maxCashCouponAmt <= 0) {
    //     return message.createMessageInfo('暂无可使用的现金券!')
    // }
    // if (!(checkIds.value.length > 0 && totalPrice.value > 0)) {
    //     return message.createMessageInfo('请选择现金券!')
    // }
    _orderInfo.value.selectIds = checkIds.value
    _orderInfo.value.money = (_orderInfo.value.goodsAmount - totalPrice.value) + _orderInfo.value.shippingFee
    _orderInfo.value.cashCouponAmt = totalPrice.value
    router.replace({
        name: RoutesName.StoreConfirmOrder,
        query: {
            orderInfo: encodeURIComponent(JSON.stringify(_orderInfo.value))
        }
    })
}
const handleBack = () => {
    router.replace({
        name: RoutesName.StoreConfirmOrder,
        query: {
            orderInfo: encodeURIComponent(JSON.stringify(_orderInfo.value))
        }
    })
}
onMounted(() => {
    if (props.orderInfo) {
        const orderInfo = JSON.parse(decodeURIComponent(props.orderInfo)) || {};
        _orderInfo.value = orderInfo
        //默认最佳选项
        bestIds.value = orderInfo.selectedCashCouponIdList || []
        getList()
    }
})
</script>

<style scoped lang="less">
@import url('@/styles/storeVar.less');

.shelve {
    filter: opacity(50%);
    color: #666666 !important;
    pointer-events: none;
}

.coupon-box {
    padding: 12px;
    box-sizing: border-box;
    background-color: #fff;
    overflow-y: auto;
    height: 100%;
    display: flex;
    flex-direction: column;

    .coupon-header {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        .header-line {
            border-radius: 99px;
            width: 4px;
            height: 16px;
            background-color: @error-color;
        }

        .header-tip {
            flex: 1;
            padding: 0 8px;
            font-size: 18px;
        }
    }

    .coupon-content {
        flex: 1;
        overflow-y: auto;

        .info {
            display: flex;
            border-radius: 12px;
            align-items: center;
            justify-content: space-between;
            padding: 8px 0;
            margin-bottom: 8px;
            box-sizing: border-box;
            background-color: #FEF3F3;
            border: 1px solid #FEF3F3;
            -webkit-mask: radial-gradient(circle at 121px 0, #0000 5px, #000 0),
                radial-gradient(circle at 121px 100%, #0000 5px, #000 0);
            -webkit-mask-size: 100% 51%;
            -webkit-mask-position: top, bottom;
            -webkit-mask-repeat: no-repeat;

            .info-left {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 90px;
                padding: 0 15px;
            }

            .info-center {
                flex: 1;
                padding: 8px 18px;
                border-left: 1px dashed rgba(239, 17, 21, 0.4);

                .header {
                    font-size: 16px;
                    font-weight: 500;
                }

                .time {
                    color: #666666;
                    font-size: 11px;
                    padding: 8px 0 6px 0;
                }

                .desc {
                    color: #999999;
                    font-size: 10px;
                }
            }

            .info-right {
                font-size: 12px;
                padding-right: 15px;
                color: @error-color;
            }
        }
    }

    .coupon-footer {
        width: 100%;
        padding: 12px;
        box-sizing: border-box;
        z-index: 999;

        .footer-content {
            display: flex;
            border-radius: 33px;
            overflow: hidden;
            box-shadow: 0px 3px 14px 0px rgba(172, 172, 172, 0.4);

            .footer-left {
                flex: 1;
                display: flex;
                align-items: baseline;
                background-color: #fff;
                overflow: hidden;
                padding: 16px 24px;
            }

            .footer-right {
                font-size: 16px;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: @error-color;
                color: #fff;
                width: 96px;
                font-weight: 500;
            }
        }

    }
}
</style>