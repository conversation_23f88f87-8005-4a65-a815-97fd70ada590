import { defHttp } from "@/services";
import { getStoreApiUrl } from "@/utils/http/urlUtils";
const enum OrderApiEnum {
  nonPres = "/h5/order/confirmOrder/nonPres",
  createOrder = "/h5/order/createOrder",
  toCheckOut = "/h5/order/toCheckOut",
  pay = "/h5/order/pay",
  queryOrderPayment = "/h5/order/queryOrderPayment",
  getOrderPayStatus = "/h5/order/getOrderPayStatus",
  getRequestNo = "/h5/order/getRequestNo",
}

const enum CouponOrderApiEnum {
  nonPres = "/h5/coupon/order/confirmOrder/nonPres",
  createOrder = "/h5/coupon/order/createOrder",
  getRequestNo = "/h5/coupon/order/getRequestNo",
  getMyCashCoupons = '/h5/couponReceiveRecord/getMyCashCoupons',
  getMyBestCashCoupons = '/h5/couponReceiveRecord/getMyBestCashCoupons',
}

const enum PointOrderApiEnum {
  nonPres = "/h5/point/order/confirmOrder/point",
  createOrder = "/h5/point/order/createOrder",
  toCheckOut = "/h5/point/order/toCheckOut",
  pay = "/h5/point/order/pay",
  queryOrderPayment = "/h5/point/order/queryOrderPayment",
}

// 非处方药订单确认
export async function nonPres(params: any) {
  return defHttp.post({
    url: getStoreApiUrl(OrderApiEnum.nonPres),
    params,
    requestConfig: {
      extendResData: ["requestNo"],
      skipCrypto: true,
    },
  });
}

export async function couponNonPres(params: any) {
  return defHttp.post({
    url: getStoreApiUrl(CouponOrderApiEnum.nonPres),
    params,
    requestConfig: {
      extendResData: ["requestNo"],
      skipCrypto: true,
    },
  });
}

//创建订单
export async function createOrder(params: any) {
  return defHttp.post({
    url: getStoreApiUrl(OrderApiEnum.createOrder),
    params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}

export async function couponCreateOrder(params: any) {
  return defHttp.post({
    url: getStoreApiUrl(CouponOrderApiEnum.createOrder),
    params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}

//前往收银台（header返回request-no）
export async function toCheckOut(params) {
  return defHttp.get({
    url: getStoreApiUrl(OrderApiEnum.toCheckOut),
    params,
    requestConfig: {
      extendResData: ["requestNo"],
      skipCrypto: true,
    },
  });
}

//主动查询微信订单
export async function queryOrderPayment(params) {
  return defHttp.post({
    url: getStoreApiUrl(OrderApiEnum.queryOrderPayment),
    params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}

//微信支付（需要request-no校验幂等性）
export async function OrderPay(params) {
  return defHttp.post({
    url: getStoreApiUrl(OrderApiEnum.pay),
    params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}

//主动查询微信订单
export async function getOrderPayStatus(params) {
  return defHttp.get({
    url: getStoreApiUrl(OrderApiEnum.getOrderPayStatus),
    params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}

//根据时间戳获取幂等性 ID（即request-no）
export async function getRequestNo() {
  return defHttp.get({
    url: getStoreApiUrl(OrderApiEnum.getRequestNo),
    params:{
      timestamp: +new Date()
    }
  });
}

export async function getCouponRequestNo() {
  return defHttp.get({
    url: getStoreApiUrl(CouponOrderApiEnum.getRequestNo),
    params:{
      timestamp: +new Date()
    }
  });
}

//积分商品订单确认
export async function pointConfirmOrder(params) {
  return defHttp.post({
    url: getStoreApiUrl(PointOrderApiEnum.createOrder),
    params,
    requestConfig: {
      extendResData: ["requestNo"],
      skipCrypto: true,
    },
  });
}

export async function pointToCheckOut(params) {
  return defHttp.get({
    url: getStoreApiUrl(PointOrderApiEnum.toCheckOut),
    params,
    requestConfig: {
      extendResData: ["requestNo"],
      skipCrypto: true,
    },
  });
}

export async function pointNonPres(params: any) {
  return defHttp.post({
    url: getStoreApiUrl(PointOrderApiEnum.nonPres),
    params,
    requestConfig: {
      extendResData: ["requestNo"],
      skipCrypto: true,
    },
  });
}

export async function pointOrderPay(params) {
  return defHttp.post({
    url: getStoreApiUrl(PointOrderApiEnum.pay),
    params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}

export async function pointQueryOrderPayment(params) {
  return defHttp.post({
    url: getStoreApiUrl(PointOrderApiEnum.queryOrderPayment),
    params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**查询我的现金福利券 */
export async function queryMyCashCoupon(params = {}) {
  return defHttp.get({
    url: getStoreApiUrl(CouponOrderApiEnum.getMyCashCoupons),
    params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}

/**查询我的最佳现金福利券组合 */
export async function queryMyBestCashCoupon(params) {
  return defHttp.get({
    url: getStoreApiUrl(CouponOrderApiEnum.getMyBestCashCoupons),
    params,
    requestConfig: {
      skipCrypto: true,
    },
  });
}