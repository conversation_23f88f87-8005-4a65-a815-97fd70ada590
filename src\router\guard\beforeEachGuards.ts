import NProgress from "nprogress";
import { useMessages } from "@/hooks/useMessage";
import { useSystemStoreWithoutSetup } from "@/stores/modules/system";
import { useWindowMessage } from "@/hooks/useWindowMessage";
import { clearStorage, createCacheStorage } from "@/utils/cache/storageCache";
import { CacheConfig } from "@/utils/cache/config";
import { isInFrame, isStoreMode } from "@/utils/envUtils";
import { parseUrlParams } from "@/utils/http/urlUtils";
import { useUserStoreWithoutSetup } from "@/stores/modules/user";
import { stBeforeEachGuardsList } from "./Store/BeforeEachGuards";
import type { NavigationGuard } from "vue-router";
import { RoutesName } from "@/enums/routes";

const {initWindowMessage} = useWindowMessage()
initWindowMessage()
NProgress.configure({ showSpinner: false });
const startProgress:NavigationGuard = (to, from, next) => {
  if (!NProgress.isStarted()) {
    NProgress.start();
  }
  const systemStore = useSystemStoreWithoutSetup()
  if(!systemStore.entryUrl){
    systemStore.setEntryUrl(location.href)
  }
  next();
};

const clearAllMessage:NavigationGuard = (to, from, next) => {
  const { destoryMessage } = useMessages();
  destoryMessage();
  next();
};

const isSaveFatherOrigin:NavigationGuard = (to,from,next)=>{
  const FatherOriginStoreage = createCacheStorage(CacheConfig.FatherOrigin)
  if(isInFrame()){
    const params = parseUrlParams(location.search)
    if(params.f){
      FatherOriginStoreage.set(decodeURIComponent(params.f as string))
    }
    next()
  }
  else{
    FatherOriginStoreage.remove()
    next()
    return
  }
}

const unResiterHandler:NavigationGuard = async(to, from, next) => {
  const isStore = isStoreMode();
  if (!to.matched.length) {
    const userStore = useUserStoreWithoutSetup();
    if (isStore?userStore.storeToken:userStore.token) {
      userStore.$reset();
      clearStorage();
      if(to.path.indexOf('scrm')!= -1 ){
        next("/9009");
      }
      else{
        next("/404");
      }
    } 
    else {
      if(isStore){
        next({
          name:RoutesName.StoreHome,
          params:to.params,
          query:to.query
        })
      }
      else{
        next(`/check`);
      }
    }
  } 
  else{
    next();
  }
};

export const beforeEachGuardsList = [
  startProgress, 
  unResiterHandler,
  isSaveFatherOrigin,
  ...stBeforeEachGuardsList,
  clearAllMessage
]
