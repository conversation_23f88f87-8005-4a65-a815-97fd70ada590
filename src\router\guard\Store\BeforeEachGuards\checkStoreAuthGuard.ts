import type { NavigationGuard } from "vue-router";
import { Exception_List } from "../constants";
import { parseUrlParams } from "@/utils/http/urlUtils";
import { useUserStoreWithoutSetup } from "@/stores/modules/user";
import { createCacheStorage } from "@/utils/cache/storageCache";
import { CacheConfig } from "@/utils/cache/config";
import { getStoreLoginWxappID, storeLogin, type StoreUserInfo, storeUSerInfoRefresh } from "@/services/storeApi";
import { RoutesName } from "@/enums/routes";
import { isObject } from "@/utils/isUtils";
import { bindStoreBelong } from "../utils";

export const checkStoreAuthGuard:NavigationGuard = async function (to, from, next){
 if (to.matched.length && !Exception_List.includes(to.path)) {
      const params = parseUrlParams(location.search)
      const filterPath = [
        RoutesName.StoreDetail,
        RoutesName.StoreConfirmOrder,
        RoutesName.StoreCashier,
        RoutesName.StoreHome,
        RoutesName.StoreOrderDetail
      ]
      if(!location.search){
        next("/9000");
        return
      }
      else{
        const storePathNameStorage = createCacheStorage(CacheConfig.StorePathName)
        const userStore = useUserStoreWithoutSetup();
        const tokenStorage = createCacheStorage(CacheConfig.StoreToken);
        const _tokenCache = tokenStorage.get();
        const userConfigStorage = createCacheStorage(CacheConfig.StoreUserInfo);
        const _userInfoCache = userConfigStorage.get();
        const loginRetryTimeStorage = createCacheStorage(CacheConfig.LoginRetryTime);
        const _loginRetryTimeCache = loginRetryTimeStorage.get() ? Number(loginRetryTimeStorage.get()) : 0;
        const errorStorage = createCacheStorage(CacheConfig.Error)
        errorStorage.remove()
        let appId = ''
        try{
          const{sgAppId} = await getStoreLoginWxappID()
          appId = sgAppId
        }
        catch(e){
          next("/9003"); 
          return
        }
        if((!_tokenCache && !params.code)){
          if(to.name == RoutesName.StoreLogin){
            next()
            return
          }
          else{
            /** 基于账号设置 - 退出登录 */
            if (to.name == RoutesName.StoreAccountSetting){
              storePathNameStorage.set('/st/main');
            } else {
              storePathNameStorage.set(location.pathname);
            }
            next({
              name:RoutesName.StoreLogin,
              params:to.params,
              query:to.query
            })
            return
          }
        }
        else{
          storePathNameStorage.remove()
          if(!_userInfoCache){
            try{
              let resp:StoreUserInfo
              if(_tokenCache){
                resp = await storeUSerInfoRefresh() 
              }
              else{
                resp = await storeLogin({
                  code:params.code as string,
                  // state:params.state as string
                })
              }
              const {token,...userInfo} = resp
              if(!token){
                userStore.clearLoginStatus()
                next("/9001");
                return
              }
              else{
                userStore.setToken(token,appId);
                userStore.setStoreToken(token,appId)
                userStore.setStoreUserInfo(userInfo)
              }
              // 绑定门店
              if(isObject(to.redirectedFrom) && to.redirectedFrom.name == RoutesName.StoreInviteMember){
                // 清除暂不填写收货地址缓存
                const hasShowFillAddressFormStorage = createCacheStorage(CacheConfig.StoreHasShowFillAddressForm);
                if (hasShowFillAddressFormStorage) {
                  hasShowFillAddressFormStorage.remove();
                }

                await bindStoreBelong();
              }

              if(to.name == RoutesName.StoreLogin){
                next({
                  name:RoutesName.StoreHome,
                  params:to.params,
                  query:to.query
                })
              }
              else{
                next()
              }
              return
            }
            catch(e){
              if (_loginRetryTimeCache < Number(import.meta.env.VITE_LOGIN_RETRY_TIME_MAX)) {
                loginRetryTimeStorage.set(_loginRetryTimeCache + 1)
                userStore.clearLoginStatus()
                storePathNameStorage.set(location.pathname)
                next({
                  name:RoutesName.StoreLogin,
                  params:to.params,
                  query:to.query
                })
                return
              }
              else {
                loginRetryTimeStorage.remove()
                userStore.clearLoginStatus()
                try {
                }
                catch (e) { }
                finally {
                  next("/9002");
                }
              }
              return
            
            }
          }
          else{
            if(isObject(to.redirectedFrom) && to.redirectedFrom.name == RoutesName.StoreInviteMember){
              // 清除暂不填写收货地址缓存
              const hasShowFillAddressFormStorage = createCacheStorage(CacheConfig.StoreHasShowFillAddressForm);
              if (hasShowFillAddressFormStorage) {
                hasShowFillAddressFormStorage.remove();
              }

              await bindStoreBelong();
            }
            next()
            return;
          }
            
        }
      }
    }
  else {
    next()
  }
}